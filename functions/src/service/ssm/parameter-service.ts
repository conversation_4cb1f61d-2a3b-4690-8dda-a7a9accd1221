import { SSMClient, GetParameterCommand, GetParametersCommand } from "@aws-sdk/client-ssm";

export interface SSMParameter {
  name: string;
  value: string;
}

export class ParameterService {
  private static instance: ParameterService;
  private client: SSMClient;
  private cache: Map<string, { value: string; timestamp: number }> = new Map();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  private constructor() {
    this.client = new SSMClient({ region: process.env.AWS_REGION || "us-east-1" });
  }

  public static getInstance(): ParameterService {
    if (!ParameterService.instance) {
      ParameterService.instance = new ParameterService();
    }
    return ParameterService.instance;
  }

  async getParameter(name: string, useCache: boolean = true): Promise<string> {
    if (useCache && this.isCacheValid(name)) {
      return this.cache.get(name)!.value;
    }

    try {
      const command = new GetParameterCommand({
        Name: name,
        WithDecryption: true, // Important: decrypts SecureString parameters
      });

      const response = await this.client.send(command);

      if (!response.Parameter?.Value) {
        throw new Error(`Parameter ${name} not found or has no value`);
      }

      const value = response.Parameter.Value;

      // Cache the result
      if (useCache) {
        this.cache.set(name, { value, timestamp: Date.now() });
      }

      return value;
    } catch (error) {
      throw new Error(`Failed to retrieve parameter ${name}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  async getParameters(names: string[], useCache: boolean = true): Promise<SSMParameter[]> {
    const uncachedNames: string[] = [];
    const result: SSMParameter[] = [];

    // Check cache for each parameter
    if (useCache) {
      for (const name of names) {
        if (this.isCacheValid(name)) {
          result.push({ name, value: this.cache.get(name)!.value });
        } else {
          uncachedNames.push(name);
        }
      }
    } else {
      uncachedNames.push(...names);
    }

    // Fetch uncached parameters
    if (uncachedNames.length > 0) {
      try {
        const command = new GetParametersCommand({
          Names: uncachedNames,
          WithDecryption: true,
        });

        const response = await this.client.send(command);

        if (response.Parameters) {
          for (const param of response.Parameters) {
            if (param.Name && param.Value) {
              result.push({ name: param.Name, value: param.Value });

              // Cache the result
              if (useCache) {
                this.cache.set(param.Name, { value: param.Value, timestamp: Date.now() });
              }
            }
          }
        }

        // Check for invalid parameters
        if (response.InvalidParameters && response.InvalidParameters.length > 0) {
          throw new Error(`Invalid parameters: ${response.InvalidParameters.join(", ")}`);
        }
      } catch (error) {
        throw new Error(`Failed to retrieve parameters: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    return result;
  }

  clearCache(): void {
    this.cache.clear();
  }

  clearParameterCache(name: string): void {
    this.cache.delete(name);
  }

  private isCacheValid(name: string): boolean {
    const cached = this.cache.get(name);
    if (!cached) return false;

    return Date.now() - cached.timestamp < this.CACHE_TTL;
  }
}

export const parameterService = ParameterService.getInstance();
